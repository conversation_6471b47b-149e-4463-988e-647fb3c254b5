import { Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import { StatusCodes } from 'http-status-codes';
import User from '../models/User';
import { accountMail } from '../utils/sendEmail';
import passport from 'passport';

const JWT_SECRET = process.env.JWT_SECRET;
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

if (!JWT_SECRET) {
  throw new Error('JWT_SECRET is not defined in the environment variables');
}

// Helper function to generate JWT token
const generateToken = (userId: string, role: string) => {
  const payload = { _id: userId, role: role };
  return jwt.sign(payload, JWT_SECRET, { expiresIn: '30d' });
};

// Helper function to create user response object
const createUserResponse = (user: any) => ({
  id: user._id,
  fullname: user.fullname,
  email: user.email,
  isEmailVerified: user.isEmailVerified,
  role: user.role,
  ProfilePicture: user.ProfilePicture,
  authProvider: user.authProvider,
  isGoogleUser: user.isGoogleUser
});

export const userSignup = async (req: Request, res: Response) => {
  try {
    const { email, fullname, phoneNumber, countryCode, password, confirmPassword, role } = req.body;

    if (!email) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Email is required' });
    }
    if (!fullname) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Full name is required' });
    }
    if (!phoneNumber) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Phone number is required' });
    }
    if (!password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Password is required' });
    }
    if (!confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Confirm password is required' });
    }
    if (!role) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Role is required' });
    }

    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }

    if (role !== 'user') {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invalid role. only user role is allowed' });
    }

    // Check if user already exists
    let user = await User.findOne({ email });
    if (user) {
      return res.status(StatusCodes.CONFLICT).json({ message: 'User already exists' });
    }

    const newUser = new User({
      email: email.toLowerCase(),
      fullname,
      phoneNumber,
      countryCode,
      password,
      role,
      authProvider: 'local',
      isGoogleUser: false,
      isEmailVerified: false,
      isBrandOwnerVerified: false,
      isBrandOwner: false
    });

    await newUser.save();

    // Generate 6-digit OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString();

    // Send OTP to user's email
    await accountMail(email, "Reset Password", otp, fullname, res);

    // Set OTP value to null before storing it (ensures old value is cleared)
    newUser['otp'] = "";
    newUser['otpCreatedAt'] = new Date();   // Store the current time as OTP generation time
    newUser['otp'] = otp;   // Store the new OTP
    await newUser.save();

    return res.status(StatusCodes.OK).json({
      message: 'User created. OTP sent to your email',
      user: createUserResponse(newUser)
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error sending OTP', error });
  }
};

export const login = async (req: Request, res: Response) => {
  try {
    const { email, password, role } = req.body;

    if (!email || !password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'All fields are required' });
    }
    if (role !== 'user' && role !== 'brandOwner') {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invalid role' });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }

    // Check if user is trying to login with password but account is Google-only
    if (user.authProvider === 'google' && user.isGoogleUser && !user.password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ 
        message: 'Please login with Google. This account was created using Google OAuth.' 
      });
    }

    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid credentials' });
    }

    if (user.role !== role) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid role. you are not authorized to access this resource' });
    }

    if (!user.isEmailVerified) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Email not verified' });
    }

    if (user.role === 'brandOwner' && !user.isBrandOwnerVerified) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Business account not approved yet' });
    }

    // Generate JWT token
    const accessToken = generateToken(user._id.toString(), user.role);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Login successful',
      user: createUserResponse(user),
      accessToken
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error logging in', error
    });
  }
};

export const forgotPassword = async (req: Request, res: Response) => {
  try {
    const { email, password, confirmPassword } = req.body;
    if (!email) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Email is required' });
    }
    if (!password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'New password is required' });
    }
    if (!confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Confirm password is required' });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }

    // Check if user is Google-only account
    if (user.authProvider === 'google' && user.isGoogleUser && !user.password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ 
        message: 'This account uses Google authentication. Password reset is not available.' 
      });
    }

    const isMatch = await user.comparePassword(password);
    if (isMatch) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'New password cannot be the same as the old password' });
    }
    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }

    user.password = password;
    await user.save();
    return res.status(StatusCodes.OK).json({ message: 'Password changed successfully' });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error changing password', error });
  }
};

export const adminSignup = async (req: Request, res: Response) => {
  try {
    const { email, fullname, phoneNumber, password, confirmPassword, role } = req.body;

    if (!email) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Email is required' });
    }
    if (!fullname) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Full name is required' });
    }
    if (!phoneNumber) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Phone number is required' });
    }
    if (!password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Password is required' });
    }
    if (!confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Confirm password is required' });
    }
    if (!role) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Role is required' });
    }

    if (password !== confirmPassword) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Passwords do not match' });
    }

    if (role !== 'admin') {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'Invalid role' });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(StatusCodes.CONFLICT).json({ message: 'User already exists' });
    }

    const newUser = new User({
      email: email.toLowerCase(),
      fullname,
      phoneNumber,
      password,
      role,
      authProvider: 'local',
      isGoogleUser: false,
      isEmailVerified: true,
      isBrandOwnerVerified: false
    });

    await newUser.save();

    return res.status(StatusCodes.OK).json({
      message: 'Admin created successfully',
      user: createUserResponse(newUser)
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({ message: 'Error creating admin', error });
  }
};

export const adminLogin = async (req: Request, res: Response) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(StatusCodes.BAD_REQUEST).json({ message: 'All fields are required' });
    }

    const user = await User.findOne({ email: email.toLowerCase() });
    if (!user) {
      return res.status(StatusCodes.NOT_FOUND).json({ message: 'User not found' });
    }

    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid credentials' });
    }

    if (user.role !== 'admin') {
      return res.status(StatusCodes.UNAUTHORIZED).json({ message: 'Invalid role. you are not authorized to access this resource' });
    }

    // Generate JWT token
    const accessToken = generateToken(user._id.toString(), user.role);

    return res.status(StatusCodes.OK).json({
      success: true,
      message: 'Login successful',
      user: createUserResponse(user),
      accessToken
    });
  } catch (error) {
    console.error(error);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      success: false,
      message: 'Error logging in', error
    });
  }
};

// Google OAuth Routes
export const googleAuth = (req: Request, res: Response, next: any) => {
  const { role } = req.query;
  
  // Store role in session for callback
  if (req.session) {
    (req.session as any).oauthRole = role || 'user';
  }
  
  passport.authenticate('google', {
    scope: ['profile', 'email']
  })(req, res, next);
};

export const googleCallback = (req: Request, res: Response, next: any) => {
  passport.authenticate('google', { session: false }, async (err: any, user: any) => {
    try {
      if (err) {
        console.error('Google OAuth error:', err);
        return res.redirect(`${FRONTEND_URL}/login?error=oauth_error`);
      }

      if (!user) {
        return res.redirect(`${FRONTEND_URL}/login?error=oauth_failed`);
      }

      // Get role from session or default to 'user'
      const requestedRole = (req.session as any)?.oauthRole || 'user';
      
      // Validate role
      if (requestedRole !== 'user' && requestedRole !== 'brandOwner') {
        return res.redirect(`${FRONTEND_URL}/login?error=invalid_role`);
      }

      // Check if user's role matches requested role
      if (user.role !== requestedRole) {
        // Update user's role if they're trying to access brandOwner
        if (requestedRole === 'brandOwner' && user.role === 'user') {
          user.role = 'brandOwner';
          user.isBrandOwner = true;
          // Note: isBrandOwnerVerified should be handled by admin
          await user.save();
        } else {
          return res.redirect(`${FRONTEND_URL}/login?error=role_mismatch`);
        }
      }

      // Check brandOwner verification
      if (user.role === 'brandOwner' && !user.isBrandOwnerVerified) {
        return res.redirect(`${FRONTEND_URL}/login?error=brand_owner_not_verified`);
      }

      // Generate JWT token
      const accessToken = generateToken(user._id.toString(), user.role);

      // Clear session role
      if (req.session) {
        delete (req.session as any).oauthRole;
      }

      // Redirect to frontend with token
      res.redirect(`${FRONTEND_URL}/auth/callback?token=${accessToken}`);

    } catch (error) {
      console.error('Error in Google callback:', error);
      res.redirect(`${FRONTEND_URL}/login?error=callback_error`);
    }
  })(req, res, next);
};