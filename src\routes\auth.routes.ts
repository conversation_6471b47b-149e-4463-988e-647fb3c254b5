import { Router } from 'express';
import { 
  userSignup, 
  login, 
  forgotPassword,
  googleAuth,
  googleCallback
} from '../controllers/index';
import { 
  resetPasswordGenerateOTP, 
  verifyOtp,
  verifySignupOTP, 
} from '../controllers/helpers/OTP';

const router = Router();

// Regular auth routes
router.post('/signup', userSignup);
router.post('/login', login);
router.post('/resend-otp', resetPasswordGenerateOTP)
router.post('/verify-otp', verifySignupOTP);

// Password Reset Routes
router.post('/forgot-password', resetPasswordGenerateOTP);
router.post('/forgot-password/verify-otp', verifyOtp);
router.post('/forgot-password/reset', forgotPassword);

// Google OAuth Routes
router.get('/google', googleAuth);
router.get('/google/callback', googleCallback);

export default router;