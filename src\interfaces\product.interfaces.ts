import { Document, Types } from 'mongoose';

export type ProductSize = 'XS' | 'S' | 'M' | 'L' | 'XL' | 'XXL' | 'XXXL';

export interface IProduct extends Document {
  _id: Types.ObjectId;
  productName: string;
  productDescription: string;
  productCategory: Types.ObjectId;
  productSubCategory: Types.ObjectId;
  shop: Types.ObjectId;
  tags: string[];
  sizeChartImage: string;
  isActive: boolean;
  productMedia: string[];
  price: number;
  sizes: ProductSize[];
  colors: string[];
  stockQuantity: number;
  stockSold: number;
  isLowStock: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICreateProductRequest {
  productName: string;
  productDescription: string;
  productCategory: string;
  productSubCategory: string;
  sizeChartImage: string;
  shop: string;
  tags: string[];
  productMedia: string[];
  price: number;
  sizes: ProductSize[];
  colors: string[];
  stockQuantity: number;
}

export interface IUpdateProductRequest {
  productName?: string;
  productDescription?: string;
  productCategory?: string;
  productSubCategory?: string;
  sizeChartImage?: string;
  tags?: string[];
  isActive?: boolean;
  productMedia?: string[];
  price?: number;
  sizes?: ProductSize[];
  colors?: string[];
  productImagesToDelete?: string[];
  stockQuantity?: number;
}

export interface IMarkAsSoldRequest {
  quantity: number;
}
