import dotenv from 'dotenv';
import express from 'express';
import { createServer } from 'http';
import connectDB from './config/db';
import cors from "cors";
import bodyParser from "body-parser";
import helmet from "helmet";
import { sendWelcomeEmail } from './utils/testsmtp';
import { Request, Response } from 'express';
import session from "express-session";
import passport from './config/passport'; // Health check endpoint
// import { sendOtpEmail } from './utils/emailjs';
//import { rateLimiter } from "./utils/rateLimit";
import { StatusCodes } from "http-status-codes";
// import authRoutes from './routes/auth.routes';
// import shopRoutes from './routes/shop.routes';
// import categoryRoutes from './routes/category.routes';
// import subcategoryRoutes from './routes/subcategory.routes';
// import productRoutes from './routes/product.routes';
// import adminRoutes from './routes/admin.routes';
// import wishlistRoutes from './routes/wishlist.routes';
// import userRoutes from './routes/user.routes';
// import brandOwnerRoutes from './routes/brandOwner.routes';
// import customProductRoutes from './routes/customProduct.routes';
// import cartRoutes from './routes/cart.routes';
// import orderRoutes from './routes/order.routes';
// import businessOrderRoutes from './routes/businessOrder.routes';
// import checkoutRoutes from './routes/checkout.routes';
// import addressRoutes from './routes/address.routes';
import {
  authRoutes,
  shopRoutes,
  categoryRoutes,
  subcategoryRoutes,
  productRoutes,
  adminRoutes,
  wishlistRoutes,
  userRoutes,
  brandOwnerRoutes,
  customProductRoutes,
  cartRoutes,
  orderRoutes,
  businessOrderRoutes,
  checkoutRoutes,
  addressRoutes
} from './routes/index'

// ----------- Config -------------------
dotenv.config();

// ----------- Server -------------------
const app = express();
const server = createServer(app);
const port = process.env.PORT || 5001;

// ---------- Middlewares ----------------------------
app.use(bodyParser.json({ limit: "50mb" }));
app.use(bodyParser.urlencoded({ limit: "50mb", extended: true }));

// CORS configuration for OAuth
app.use(cors({
  origin: [
    process.env.FRONTEND_URL || 'http://localhost:3000',
    'http://localhost:3000',
    'http://localhost:3001'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-requested-with'],
}));

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      connectSrc: ["'self'", "https://accounts.google.com", "https://oauth2.googleapis.com"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

// Session configuration for OAuth
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-session-secret-key',
  resave: false,
  saveUninitialized: false,
  cookie: {
    secure: process.env.NODE_ENV === 'production',
    httpOnly: true,
    maxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
}));

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

//app.use(rateLimiter());

// ---------- Routes ----------------------------
app.use('/api/auth', authRoutes);
app.use('/api/shops', shopRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/subcategories', subcategoryRoutes);
app.use('/api/products', productRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/wishlist', wishlistRoutes);
app.use('/api/users', userRoutes);
app.use('/api/brand-owners', brandOwnerRoutes);
app.use('/api/custom-products', customProductRoutes);
app.use('/api/cart', cartRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/business/orders', businessOrderRoutes);
app.use('/api/checkout', checkoutRoutes);
app.use('/api/addresses', addressRoutes);
// app.post('/send-welcome-email', (req: Request, res: Response) => {
//   // Extract email address from the request body
//   const { email } = req.body;

//   if (!email) {
//     return res.status(400).json({ message: 'Email is required' });
//   }

//   // Send the welcome email
//   sendOtpEmail(email, '1234', '2:00 PM');

//   // Respond to the client
//   return res.status(200).json({ message: 'Welcome email sent!' });
// });



app.get('/api/health', (req: Request, res: Response) => {
  res.status(200).json({ 
    status: 'OK', 
    message: 'Server is running',
    timestamp: new Date().toISOString()
  });
});

// Handle 404 for API routes
app.use('/api/*', (req: Request, res: Response) => {
  res.status(404).json({ message: 'API endpoint not found' });
});

// Global error handler
app.use((err: any, req: Request, res: Response, next: any) => {
  console.error('Global error handler:', err);
  res.status(500).json({ 
    message: 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { error: err.message })
  });
});

// Connect to MongoDB
connectDB();

// Start server
server.listen(port, () => {
  console.log('Server is running on port ', port);
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('Frontend URL:', process.env.FRONTEND_URL || 'http://localhost:3000');
});